# This .gitignore file should be placed at the root of your Unity project directory
#
# Get latest from https://github.com/github/gitignore/blob/main/Unity.gitignore
#
/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/[Ll]ogs/
/[Uu]ser[Ss]ettings/

# HotReload
/Packages/com.singularitygroup.hotreload/

# MemoryCaptures can get excessive in size.
# They also could contain extremely sensitive data
/[Mm]emoryCaptures/

# Recordings can get excessive in size
/[Rr]ecordings/

/ignore.conf
*.private
*.private.meta
^*.private.[0-9]+$
^*.private.[0-9]+.meta$
.collabignore
**/Assets/AddressableAssetsData/*/*.bin*
**/assets/addressableassetsdata/*/*.bin*
**/Assets/StreamingAssets/aa.meta
**/assets/streamingassets/*/aa/*

# Unity generated PlasticSCM flies
/Assets/Plugins/PlasticSCM*
/assets/plugins/PlasticSCM*

# Asset store tools plugin
[Aa]ssets/AssetStoreTools*
**/assets/assetstoretools

# Visual Studio cache directory
.vs/

# Visual Studio code cache directory
.vscode/

# JetBrain cache directory
.idea/
/[Aa]ssets/Plugins/Editor/JetBrains*

# Cursor
.cursor/
.cursorrules

# Windsurf
.windsurf/
.windsurfrules

# Trae
.trae/

# Claude Code
.claude/
CLAUDE.md
.mcp.json

# Gemini
.gemini/
GEMINI.md

# Augment
.augment/
.augment-guidelines

# Gradle cache directory
.gradle/

# Autogenerated VS/MD/Consulo solution and project files
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity generated meta files
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity generated file on crash reports
sysinfo.txt

# Builds
*.apk
*.aab
*.unitypackage
*.app

# Crashlytics generated file
crashlytics-build.properties

# Packed Addressables
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/*/*.bin*

# Temporary auto-generated Android Assets
/[Aa]ssets/[Ss]treamingAssets/aa.meta
/[Aa]ssets/[Ss]treamingAssets/aa/*

# SVN
.svn/

# Windows files
Thumbs.db
Desktop.ini

# Mac files
.DS_Store*
*.xcodeproj

# TopDown Engine
/[Aa]ssets/MMData/
/[Aa]ssets/MMData.meta

# Addressables —— 平台构建产物（Android、Windows、iOS 等）
/Assets/AddressableAssetsData/*/

# 但保留核心配置与模板
!/Assets/AddressableAssetsData/AssetGroups/
!/Assets/AddressableAssetsData/AssetGroupTemplates/
!/Assets/AddressableAssetsData/DataBuilders/
!/Assets/AddressableAssetsData/*.asset

# Addressables 远程包 / CCD 输出
/ServerData/
/RemoteBuilds/
