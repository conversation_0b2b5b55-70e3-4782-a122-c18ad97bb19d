# Unity Localization 完整使用指南

## 目录
1. [概述](#概述)
2. [安装与配置](#安装与配置)
3. [基础功能](#基础功能)
4. [高级功能](#高级功能)
5. [API使用与代码示例](#api使用与代码示例)
6. [资产本地化](#资产本地化)
7. [最佳实践](#最佳实践)
8. [工作流程](#工作流程)
9. [常见问题与解决方案](#常见问题与解决方案)
10. [参考资源](#参考资源)

## 概述

Unity Localization是Unity官方提供的本地化解决方案，用于帮助开发者创建支持多语言的游戏和应用程序。

### 主要特性

- **字符串本地化**：根据不同地区显示不同的文本内容
- **资产本地化**：根据地区使用不同的资产（纹理、音频、模型等）
- **Smart String智能字符串**：支持变量插值、复数形式、条件格式化等高级功能
- **Addressables集成**：基于Addressables系统，支持异步加载和远程资源
- **编辑器工具**：提供直观的编辑器界面进行本地化管理
- **导入导出**：支持CSV、XLIFF、Google Sheets等格式
- **伪本地化**：在早期测试阶段模拟不同语言的显示效果

### 系统要求

- Unity 2020.3 或更高版本
- 推荐安装：TextMeshPro包（用于更好的文本渲染）
- 自动依赖：Addressables包（会自动安装）

## 安装与配置

### 1. 安装Unity Localization包

```
1. 打开Package Manager（Window > Package Manager）
2. 点击左上角"+"按钮，选择"Add package from git URL..."
3. 输入：com.unity.localization
4. 或者在Package Manager中搜索"Localization"并安装
```

### 2. 创建Localization Settings

```
1. 打开 Edit > Project Settings > Localization
2. 点击"Create"按钮创建Localization Settings资产
3. Settings会自动保存在Assets/Settings目录下
```

### 3. 创建Locale（语言/地区）

```
1. 在Project Settings > Localization中
2. 点击"Locale Generator"按钮
3. 在弹出窗口中选择需要支持的语言：
   - English (en)
   - Chinese Simplified (zh-Hans)
   - Japanese (ja)
   - 等等...
4. 点击"Generate Locales"
5. 生成的Locale资产会保存在Assets/Settings/Locales目录
```

### 4. 设置默认Locale

```csharp
// 在Localization Settings中设置：
// Available Locales：所有可用的语言列表
// Project Locale Identifier：项目默认语言
```

## 基础功能

### 1. 创建String Table Collection

String Table Collection用于存储所有文本翻译。

```
1. 右键点击Project窗口
2. Create > Localization > String Table Collection
3. 命名为"UI_Strings"或其他合适的名称
4. 选择支持的语言
5. 点击"Create"
```

### 2. 添加字符串条目

在String Table Collection编辑器中：

```
Key: MainMenu_Title
English: Monster Survivors
Chinese (Simplified): 怪物幸存者
Japanese: モンスターサバイバー
```

### 3. UI文本本地化

#### 使用Localize String Event组件

```csharp
// 1. 在Text或TextMeshPro组件上右键
// 2. 选择"Localize"
// 3. 自动添加LocalizeStringEvent组件并连接

// 或手动添加：
1. 添加LocalizeStringEvent组件
2. 设置String Reference：
   - Table: UI_Strings
   - Table Entry: MainMenu_Title
3. 在Update String下连接到Text组件的text属性
```

#### 基础代码示例

```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;

public class BasicLocalization : MonoBehaviour
{
    // 定义本地化字符串引用
    [SerializeField] 
    private LocalizedString localizedTitle = new LocalizedString
    {
        TableReference = "UI_Strings",
        TableEntryReference = "MainMenu_Title"
    };

    void Start()
    {
        // 获取本地化文本
        string title = localizedTitle.GetLocalizedString();
        Debug.Log($"当前标题: {title}");

        // 监听语言变化
        LocalizationSettings.SelectedLocaleChanged += OnLocaleChanged;
    }

    void OnLocaleChanged(Locale newLocale)
    {
        Debug.Log($"语言切换到: {newLocale.LocaleName}");
        // UI会自动更新，无需手动处理
    }
}
```

### 4. 切换语言

```csharp
using System.Collections;
using UnityEngine;
using UnityEngine.Localization.Settings;

public class LanguageSwitcher : MonoBehaviour
{
    // 切换到指定索引的语言
    public void SetLanguageByIndex(int index)
    {
        StartCoroutine(SetLocale(index));
    }

    IEnumerator SetLocale(int localeIndex)
    {
        // 等待本地化系统初始化
        yield return LocalizationSettings.InitializationOperation;

        // 设置语言
        LocalizationSettings.SelectedLocale = 
            LocalizationSettings.AvailableLocales.Locales[localeIndex];
    }

    // 通过语言代码切换
    public void SetLanguageByCode(string localeCode)
    {
        var locale = LocalizationSettings.AvailableLocales.GetLocale(localeCode);
        if (locale != null)
        {
            LocalizationSettings.SelectedLocale = locale;
        }
    }
}
```

## 高级功能

### 1. Smart String 智能字符串

Smart String提供了强大的字符串格式化功能，支持变量插值、条件逻辑、复数形式等。

#### 启用Smart String

在String Table Entry中勾选"Smart"复选框。

#### 变量插值

```csharp
// String Table中的Smart String：
// Key: Player_Score
// Value: "{PlayerName} 的得分是 {Score:N0}"

public class SmartStringExample : MonoBehaviour
{
    [SerializeField]
    private LocalizedString scoreString = new LocalizedString
    {
        TableReference = "UI_Strings",
        TableEntryReference = "Player_Score"
    };

    void ShowScore()
    {
        // 方式1：使用匿名对象
        var values = new { PlayerName = "小明", Score = 1500 };
        string text = scoreString.GetLocalizedString(values);
        // 输出: "小明 的得分是 1,500"

        // 方式2：使用字典
        var dict = new Dictionary<string, object>
        {
            ["PlayerName"] = "小红",
            ["Score"] = 2000
        };
        text = scoreString.GetLocalizedString(dict);
    }
}
```

#### 复数形式处理

```csharp
// Smart String示例：
// "{ItemCount:plural:=0{没有物品}|=1{一个物品}|other{# 个物品}}"

public class PluralExample : MonoBehaviour
{
    [SerializeField]
    private LocalizedString itemCountString;

    void ShowItemCount(int count)
    {
        var values = new { ItemCount = count };
        string text = itemCountString.GetLocalizedString(values);
        // count = 0: "没有物品"
        // count = 1: "一个物品"  
        // count = 5: "5 个物品"
    }
}
```

#### 条件格式化

```csharp
// Smart String: "{Health:cond:>80{健康}|>30{受伤}|other{危险}}"

public class ConditionalFormat : MonoBehaviour
{
    [SerializeField]
    private LocalizedString healthStatus;

    void UpdateHealthStatus(float health)
    {
        var values = new { Health = health };
        string status = healthStatus.GetLocalizedString(values);
        // health = 100: "健康"
        // health = 50: "受伤"
        // health = 10: "危险"
    }
}
```

### 2. 格式化选项

#### 数字格式化

```csharp
// Smart String示例：
// "金币: {Gold:C}"          // 货币格式：$1,234.56
// "百分比: {Percent:P2}"    // 百分比：85.50%
// "距离: {Distance:N2}米"   // 数字：1,234.56米

public class NumberFormatting : MonoBehaviour
{
    [SerializeField]
    private LocalizedString formattedString;

    void ShowFormattedValues()
    {
        var values = new 
        { 
            Gold = 1234.56f,
            Percent = 0.855f,
            Distance = 1234.56789f
        };
        
        string result = formattedString.GetLocalizedString(values);
    }
}
```

#### 日期时间格式化

```csharp
// Smart String: "今天是 {Today:D}"
// 长日期格式：2024年1月15日 星期一

// Smart String: "当前时间 {Now:t}"  
// 短时间格式：下午 3:30

public class DateTimeFormatting : MonoBehaviour
{
    [SerializeField]
    private LocalizedString dateTimeString;

    void ShowDateTime()
    {
        var values = new 
        { 
            Today = DateTime.Now,
            Now = DateTime.Now
        };
        
        string result = dateTimeString.GetLocalizedString(values);
    }
}
```

### 3. 全局变量

全局变量可以在多个本地化字符串中共享使用。

```csharp
using UnityEngine.Localization.SmartFormat.GlobalVariables;

public class GlobalVariableExample : MonoBehaviour
{
    void SetupGlobalVariables()
    {
        // 创建全局变量源
        var source = LocalizationSettings.StringDatabase.SmartFormatter
            .GetSourceExtension<GlobalVariablesSource>();

        // 添加变量
        var playerLevel = new IntVariable { Value = 10 };
        source.Add("PlayerLevel", playerLevel);

        // 在Smart String中使用：
        // "你的等级是 {global.PlayerLevel}"
    }

    // 更新全局变量
    void UpdatePlayerLevel(int newLevel)
    {
        var source = LocalizationSettings.StringDatabase.SmartFormatter
            .GetSourceExtension<GlobalVariablesSource>();
        
        if (source["PlayerLevel"] is IntVariable levelVar)
        {
            levelVar.Value = newLevel;
            // 所有使用该变量的UI会自动更新
        }
    }
}
```

## API使用与代码示例

### 1. LocalizedString详解

```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;
using UnityEngine.Localization.Tables;

public class LocalizedStringAPI : MonoBehaviour
{
    // 多种创建LocalizedString的方式
    
    // 方式1：使用表引用
    LocalizedString byTableReference = new LocalizedString
    {
        TableReference = "UI_Strings",
        TableEntryReference = "Welcome_Message"
    };

    // 方式2：使用表GUID和Key ID
    LocalizedString byIds = new LocalizedString
    {
        TableReference = new Guid("your-table-guid"),
        TableEntryReference = 123456789 // Key ID
    };

    // 方式3：使用构造函数
    LocalizedString byConstructor = new LocalizedString("UI_Strings", "Welcome_Message");

    // 异步获取本地化字符串
    async void GetStringAsync()
    {
        var operation = byTableReference.GetLocalizedStringAsync();
        await operation.Task;
        
        if (operation.IsDone && !string.IsNullOrEmpty(operation.Result))
        {
            Debug.Log($"本地化文本: {operation.Result}");
        }
    }

    // 带参数的本地化字符串
    void GetStringWithArguments()
    {
        var localizedString = new LocalizedString("UI_Strings", "Score_Format");
        
        // 添加本地参数
        localizedString.Arguments = new object[] { "玩家名", 1000 };
        
        // 或使用局部变量
        localizedString.Add("playerName", new StringVariable { Value = "张三" });
        localizedString.Add("score", new IntVariable { Value = 2000 });
        
        string result = localizedString.GetLocalizedString();
    }

    // 监听字符串变化
    void RegisterStringChangeHandler()
    {
        byTableReference.StringChanged += OnStringChanged;
    }

    void OnStringChanged(string newValue)
    {
        Debug.Log($"字符串已更新: {newValue}");
    }
}
```

### 2. LocalizeStringEvent组件使用

```csharp
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;

public class LocalizeStringEventExample : MonoBehaviour
{
    [SerializeField]
    private LocalizeStringEvent localizeStringEvent;
    
    [SerializeField]
    private Text uiText;

    void Start()
    {
        // 方式1：通过代码设置LocalizeStringEvent
        SetupLocalizeStringEvent();
        
        // 方式2：动态更新参数
        UpdateDynamicContent();
    }

    void SetupLocalizeStringEvent()
    {
        // 设置要本地化的字符串
        localizeStringEvent.StringReference = new LocalizedString
        {
            TableReference = "UI_Strings",
            TableEntryReference = "Dynamic_Message"
        };

        // 设置更新事件
        localizeStringEvent.OnUpdateString.RemoveAllListeners();
        localizeStringEvent.OnUpdateString.AddListener(UpdateText);
    }

    void UpdateText(string value)
    {
        if (uiText != null)
            uiText.text = value;
    }

    void UpdateDynamicContent()
    {
        // 为Smart String设置参数
        var characterData = new
        {
            CharacterName = "勇者",
            Level = 25,
            HP = 150,
            MaxHP = 200
        };

        // 更新参数并刷新字符串
        localizeStringEvent.StringReference.Arguments = new[] { characterData };
        localizeStringEvent.RefreshString();
    }
}
```

### 3. 表和条目的直接访问

```csharp
using System.Collections;
using UnityEngine;
using UnityEngine.Localization.Settings;
using UnityEngine.Localization.Tables;

public class DirectTableAccess : MonoBehaviour
{
    IEnumerator AccessStringTable()
    {
        // 等待本地化系统初始化
        yield return LocalizationSettings.InitializationOperation;

        // 获取当前语言的字符串表
        var table = LocalizationSettings.StringDatabase.GetTable("UI_Strings");
        
        if (table != null)
        {
            // 通过key获取条目
            var entry = table.GetEntry("Welcome_Message");
            if (entry != null)
            {
                Debug.Log($"原始值: {entry.Value}");
                Debug.Log($"本地化值: {entry.GetLocalizedString()}");
            }

            // 遍历所有条目
            foreach (var kvp in table)
            {
                Debug.Log($"Key: {kvp.Key}, Value: {kvp.Value.Value}");
            }
        }
    }

    // 获取所有语言的某个字符串
    IEnumerator GetAllTranslations(string key)
    {
        yield return LocalizationSettings.InitializationOperation;

        var stringTable = LocalizationSettings.StringDatabase;
        
        foreach (var locale in LocalizationSettings.AvailableLocales.Locales)
        {
            var table = stringTable.GetTable("UI_Strings", locale);
            if (table != null)
            {
                var entry = table.GetEntry(key);
                if (entry != null)
                {
                    Debug.Log($"{locale.LocaleName}: {entry.Value}");
                }
            }
        }
    }
}
```

### 4. 自定义变量源

```csharp
using UnityEngine;
using UnityEngine.Localization.SmartFormat.Core.Extensions;
using UnityEngine.Localization.SmartFormat.PersistentVariables;

[System.Serializable]
public class GameStats : IVariable
{
    public string Name => "GameStats";
    
    public int PlayerLevel { get; set; }
    public int PlayerHealth { get; set; }
    public float PlayTime { get; set; }
    
    public object GetSourceValue(ISelectorInfo selector)
    {
        switch (selector.SelectorText)
        {
            case "Level": return PlayerLevel;
            case "Health": return PlayerHealth;
            case "PlayTime": return PlayTime;
            default: return null;
        }
    }
}

public class CustomVariableExample : MonoBehaviour
{
    [SerializeField]
    private LocalizedString statsString;
    
    private GameStats gameStats = new GameStats
    {
        PlayerLevel = 10,
        PlayerHealth = 100,
        PlayTime = 120.5f
    };

    void Start()
    {
        // Smart String: "等级:{GameStats.Level} 生命:{GameStats.Health} 游戏时间:{GameStats.PlayTime:F1}分钟"
        statsString.Add("GameStats", gameStats);
        
        string result = statsString.GetLocalizedString();
        Debug.Log(result);
    }
}
```

## 资产本地化

### 1. 创建Asset Table Collection

```
1. 右键 Create > Localization > Asset Table Collection
2. 命名（如 "Textures", "Audio" 等）
3. 选择支持的语言
4. 点击 Create
```

### 2. 纹理本地化

```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;
using UnityEngine.UI;

public class LocalizeTexture : MonoBehaviour
{
    [SerializeField]
    private LocalizedSprite localizedLogo = new LocalizedSprite
    {
        TableReference = "Textures",
        TableEntryReference = "GameLogo"
    };
    
    [SerializeField]
    private Image logoImage;

    async void Start()
    {
        // 异步加载本地化精灵
        var handle = localizedLogo.LoadAssetAsync();
        await handle.Task;
        
        if (handle.Status == AsyncOperationStatus.Succeeded)
        {
            logoImage.sprite = handle.Result;
        }
    }
}
```

### 3. 音频本地化

```csharp
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;

public class LocalizeAudio : MonoBehaviour
{
    [SerializeField]
    private LocalizedAudioClip localizedBGM = new LocalizedAudioClip
    {
        TableReference = "Audio", 
        TableEntryReference = "MainTheme"
    };
    
    [SerializeField]
    private AudioSource audioSource;

    void Start()
    {
        // 使用LocalizeAudioClipEvent组件（推荐）
        var localizeEvent = gameObject.AddComponent<LocalizeAudioClipEvent>();
        localizeEvent.AssetReference = localizedBGM;
        localizeEvent.OnUpdateAsset.AddListener(UpdateAudioClip);
    }

    void UpdateAudioClip(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.clip = clip;
            audioSource.Play();
        }
    }
}
```

### 4. 预制体本地化

```csharp
using UnityEngine;
using UnityEngine.Localization;

public class LocalizePrefab : MonoBehaviour
{
    [SerializeField]
    private LocalizedGameObject localizedPrefab = new LocalizedGameObject
    {
        TableReference = "Prefabs",
        TableEntryReference = "EnemyPrefab"
    };

    async void SpawnLocalizedEnemy()
    {
        var handle = localizedPrefab.LoadAssetAsync();
        await handle.Task;
        
        if (handle.Status == AsyncOperationStatus.Succeeded)
        {
            GameObject enemyPrefab = handle.Result;
            Instantiate(enemyPrefab, transform.position, Quaternion.identity);
        }
    }
}
```

### 5. 使用Localized Property Variants

这允许你直接在Inspector中为不同语言设置不同的属性值。

```csharp
// 1. 在组件上右键，选择 "Add Localized Variant"
// 2. 选择要本地化的属性
// 3. 为每个语言设置不同的值

// 示例：为不同语言设置不同的字体大小
public class LocalizedFontSize : MonoBehaviour
{
    [SerializeField]
    private Text textComponent;
    
    // 通过Localized Property Variants，
    // 可以为中文设置更大的字体
}
```

## 最佳实践

### 1. 项目结构建议

```
Assets/
├── Localization/
│   ├── Tables/
│   │   ├── StringTables/
│   │   │   ├── UI_Strings.asset
│   │   │   ├── UI_Strings_en.asset
│   │   │   ├── UI_Strings_zh-Hans.asset
│   │   │   └── UI_Strings_ja.asset
│   │   └── AssetTables/
│   │       ├── Textures.asset
│   │       ├── Audio.asset
│   │       └── Prefabs.asset
│   └── Locales/
│       ├── en.asset
│       ├── zh-Hans.asset
│       └── ja.asset
├── Settings/
│   └── LocalizationSettings.asset
```

### 2. 命名规范

#### String Keys命名
```
// 使用下划线分隔的大写命名
MainMenu_Title
MainMenu_PlayButton
MainMenu_OptionsButton

// 使用点号分隔的层级命名
UI.MainMenu.Title
UI.MainMenu.Buttons.Play
UI.MainMenu.Buttons.Options

// 带上下文的命名
Player_Health_Low
Player_Health_Critical
Enemy_Boss_Defeated
```

#### Table命名
```
UI_Strings      // UI文本
Dialogue_NPC    // NPC对话
Tutorial_Text   // 教程文本
Item_Names      // 物品名称
Item_Descriptions // 物品描述
```

### 3. 性能优化

#### 预加载关键资源
```csharp
using UnityEngine.Localization.Settings;
using UnityEngine.ResourceManagement.AsyncOperations;

public class LocalizationPreloader : MonoBehaviour
{
    async void Start()
    {
        // 预加载所有字符串表
        await LocalizationSettings.StringDatabase.PreloadTables();
        
        // 预加载特定表
        var tableReference = "UI_Strings";
        await LocalizationSettings.StringDatabase.PreloadTables(tableReference);
        
        // 预加载资产表
        await LocalizationSettings.AssetDatabase.PreloadTables("Textures");
    }
}
```

#### 缓存本地化字符串
```csharp
public class CachedLocalization : MonoBehaviour
{
    private Dictionary<string, string> cachedStrings = new Dictionary<string, string>();
    
    string GetCachedString(string key)
    {
        if (!cachedStrings.ContainsKey(key))
        {
            var localizedString = new LocalizedString("UI_Strings", key);
            cachedStrings[key] = localizedString.GetLocalizedString();
        }
        return cachedStrings[key];
    }
    
    // 语言切换时清空缓存
    void OnLocaleChanged(Locale locale)
    {
        cachedStrings.Clear();
    }
}
```

### 4. 内存管理

```csharp
using UnityEngine.Localization;
using UnityEngine.ResourceManagement.AsyncOperations;

public class MemoryManagement : MonoBehaviour
{
    private AsyncOperationHandle<Sprite> spriteHandle;
    
    async void LoadLocalizedSprite()
    {
        var localizedSprite = new LocalizedSprite("Textures", "Logo");
        
        // 保存handle以便后续释放
        spriteHandle = localizedSprite.LoadAssetAsync();
        var sprite = await spriteHandle.Task;
        
        // 使用sprite...
    }
    
    void OnDestroy()
    {
        // 释放资源
        if (spriteHandle.IsValid())
        {
            Addressables.Release(spriteHandle);
        }
    }
}
```

### 5. 调试技巧

```csharp
public class LocalizationDebugger : MonoBehaviour
{
    [SerializeField]
    private bool showMissingTranslations = true;
    
    void Start()
    {
        // 监听缺失的翻译
        LocalizationSettings.StringDatabase.MissingTranslation += OnMissingTranslation;
    }
    
    void OnMissingTranslation(string key, long keyId, TableReference tableReference, StringTable table)
    {
        if (showMissingTranslations)
        {
            Debug.LogWarning($"缺失翻译 - Table: {tableReference}, Key: {key}");
        }
    }
}
```

## 工作流程

### 1. 导出到CSV

```csharp
// 通过编辑器：
// 1. 打开String Table Collection
// 2. 点击"Export"按钮
// 3. 选择CSV格式
// 4. 选择导出路径

// 或通过代码：
#if UNITY_EDITOR
using UnityEditor.Localization.Plugins.CSV;

public static void ExportToCSV()
{
    var collection = AssetDatabase.LoadAssetAtPath<StringTableCollection>(
        "Assets/Localization/Tables/StringTables/UI_Strings.asset");
    
    var csvExtension = new CsvExtension();
    csvExtension.Export(collection, "Assets/Exports/UI_Strings.csv");
}
#endif
```

### 2. 从CSV导入

```csharp
#if UNITY_EDITOR
public static void ImportFromCSV()
{
    var collection = AssetDatabase.LoadAssetAtPath<StringTableCollection>(
        "Assets/Localization/Tables/StringTables/UI_Strings.asset");
    
    var csvExtension = new CsvExtension();
    using (var stream = File.OpenRead("Assets/Exports/UI_Strings.csv"))
    {
        csvExtension.Import(collection, stream);
    }
    
    EditorUtility.SetDirty(collection);
    AssetDatabase.SaveAssets();
}
#endif
```

### 3. Google Sheets集成

```csharp
// 1. 安装Google Sheets Service Provider包
// 2. 设置Google Sheets凭据
// 3. 在String Table Collection中配置：
//    - 点击"Extensions"
//    - 添加"Google Sheets Extension"
//    - 配置Sheet ID和认证信息
//    - 设置同步规则
```

### 4. 版本控制建议

```
# .gitignore 建议配置

# Unity Localization - 保留所有本地化相关文件
# 重要：.meta文件必须纳入版本控制！

# Addressables - 只忽略构建产物和缓存
/Assets/AddressableAssetsData/Windows/
/Assets/AddressableAssetsData/OSX/
/Assets/AddressableAssetsData/Android/
/Assets/AddressableAssetsData/iOS/
/Assets/AddressableAssetsData/WebGL/
/Assets/AddressableAssetsData/StandaloneWindows64/
/Assets/AddressableAssetsData/StandaloneOSX/

# 忽略本地构建缓存
/Library/com.unity.addressables/
/Library/BuildCache/
/ServerData/

# 重要：以下文件必须纳入版本控制
# ✓ /Assets/Settings/LocalizationSettings.asset
# ✓ /Assets/Settings/LocalizationSettings.asset.meta
# ✓ /Assets/Localization/**  (包括所有.meta文件)
# ✓ /Assets/AddressableAssetsData/AddressableAssetSettings.asset
# ✓ /Assets/AddressableAssetsData/AssetGroups/
# ✓ /Assets/AddressableAssetsData/DataBuilders/
# ✓ /Assets/AddressableAssetsData/AssetGroupTemplates/
# ✓ /Assets/AddressableAssetsData/DefaultObject.asset
```

#### 版本控制最佳实践

1. **必须纳入版本控制的文件**：
   - 所有 `.meta` 文件（包含Unity的GUID信息）
   - LocalizationSettings.asset 及其 .meta
   - 所有 Locale 资产文件
   - 所有 Table Collection 文件（String Tables 和 Asset Tables）
   - Addressables 配置文件

2. **可以忽略的文件**：
   - Addressables 构建输出（各平台的构建结果）
   - 本地缓存文件
   - 临时文件

3. **团队协作注意事项**：
   ```bash
   # 合并冲突处理
   # 如果LocalizationSettings.asset发生冲突：
   git checkout --theirs Assets/Settings/LocalizationSettings.asset
   # 然后在Unity中重新配置需要的设置
   
   # 如果Table文件发生冲突：
   # 优先使用包含更多翻译的版本
   git merge-tool
   ```

### 5. 团队协作流程

```mermaid
graph LR
    A[策划提供文本] --> B[导出到Google Sheets]
    B --> C[翻译团队工作]
    C --> D[审核翻译]
    D --> E[同步到Unity]
    E --> F[测试验证]
    F --> G[发布]
```

## 常见问题与解决方案

### 1. 本地化系统未初始化

**问题**：调用本地化API时出现NullReferenceException

**解决方案**：
```csharp
IEnumerator WaitForLocalizationInit()
{
    // 等待初始化完成
    yield return LocalizationSettings.InitializationOperation;
    
    // 现在可以安全使用本地化功能
    var currentLocale = LocalizationSettings.SelectedLocale;
}
```

### 2. 找不到本地化字符串

**问题**：GetLocalizedString返回key而不是翻译文本

**解决方案**：
```csharp
// 1. 检查表名和key是否正确
// 2. 确保表已加载
// 3. 添加调试日志

void DebugMissingString(string tableName, string key)
{
    var table = LocalizationSettings.StringDatabase.GetTable(tableName);
    if (table == null)
    {
        Debug.LogError($"找不到表: {tableName}");
        return;
    }
    
    var entry = table.GetEntry(key);
    if (entry == null)
    {
        Debug.LogError($"找不到key: {key} 在表 {tableName}");
    }
    else
    {
        Debug.Log($"找到条目: {entry.Value}");
    }
}
```

### 3. 性能问题

**问题**：切换语言时卡顿

**解决方案**：
```csharp
public class OptimizedLocaleSwitch : MonoBehaviour
{
    // 使用异步加载
    async void SwitchLocaleAsync(Locale newLocale)
    {
        // 显示加载界面
        ShowLoadingUI(true);
        
        // 预加载新语言的资源
        await LocalizationSettings.StringDatabase.PreloadTables(newLocale);
        await LocalizationSettings.AssetDatabase.PreloadTables(newLocale);
        
        // 切换语言
        LocalizationSettings.SelectedLocale = newLocale;
        
        // 隐藏加载界面
        ShowLoadingUI(false);
    }
}
```

### 4. Smart String不工作

**问题**：变量没有被替换

**解决方案**：
```csharp
// 1. 确保勾选了"Smart"复选框
// 2. 检查变量名是否匹配
// 3. 确保传入了正确的参数

// 调试方法
void DebugSmartString(LocalizedString localizedString)
{
    // 打印原始字符串
    var table = LocalizationSettings.StringDatabase.GetTable(localizedString.TableReference);
    var entry = table?.GetEntry(localizedString.TableEntryReference);
    Debug.Log($"原始: {entry?.Value}");
    
    // 打印处理后的字符串
    var processed = localizedString.GetLocalizedString();
    Debug.Log($"处理后: {processed}");
}
```

### 5. 构建时资源丢失

**问题**：打包后某些本地化资源无法加载

**解决方案**：
```csharp
// 1. 检查Addressables配置
// 2. 确保所有本地化资源都被标记为Addressable
// 3. 在Player Settings中正确配置

// 构建前检查
#if UNITY_EDITOR
[MenuItem("Localization/Verify Build Setup")]
static void VerifyBuildSetup()
{
    // 检查是否有未标记的资源
    var settings = LocalizationEditorSettings.ActiveLocalizationSettings;
    
    // 检查所有表是否正确配置
    foreach (var table in settings.GetStringDatabase().GetAllTables())
    {
        Debug.Log($"检查表: {table.TableCollectionName}");
    }
}
#endif
```

## 参考资源

### 官方文档
- [Unity Localization Documentation](https://docs.unity3d.com/Packages/com.unity.localization@latest)
- [Unity Localization API Reference](https://docs.unity3d.com/Packages/com.unity.localization@latest/api/)
- [Unity Forum - Localization](https://forum.unity.com/forums/localization.205/)

### 教程和指南
- [Unity Learn - Localization](https://learn.unity.com/search?k=%5B%22q%3Alocalization%22%5D)
- [Localization Package Samples](https://github.com/Unity-Technologies/Localization-Package-Samples)

### 社区资源
- [Unity Localization Best Practices (Reddit)](https://www.reddit.com/r/Unity3D/)
- [Stack Overflow - Unity Localization Tag](https://stackoverflow.com/questions/tagged/unity-localization)

### 相关工具
- [POEditor](https://poeditor.com/) - 在线翻译管理平台
- [Crowdin](https://crowdin.com/) - 本地化管理平台
- [LocalizationKit](https://github.com/microsoft/LocalizationKit) - 微软的本地化工具

### 示例项目
```bash
# 克隆官方示例
git clone https://github.com/Unity-Technologies/Localization-Package-Samples.git
```

## 总结

Unity Localization包提供了完整的本地化解决方案，从基础的文本翻译到复杂的Smart String，从简单的UI本地化到完整的资产本地化。通过合理使用这个系统，可以轻松创建支持多语言的游戏和应用。

关键要点：
1. 始终等待本地化系统初始化完成
2. 合理组织表结构，使用清晰的命名规范
3. 充分利用Smart String的强大功能
4. 注意性能优化，特别是在移动平台
5. 建立良好的团队协作流程
6. 做好版本控制和资源管理

通过遵循本指南中的最佳实践，你可以构建一个高效、可维护的多语言Unity项目。